export interface WorkGroupCreationPlanProps {
  getBadgeCount: Callback;
  handleClose: Callback;
  refetch: Callback;
  title: string;
}

export interface WorkGroupCreationPlanInitial {
  plan: Plan;
}

interface Plan {
  buttons: ApiDefaultKeys;
  schedulerValue: string | null;
  searchInputs: Record<string, unknown>;
  table: Table;
}

export interface SearchInputs extends TitleAndKey {
  /** Тип инпута */
  type:
    | 'default'
    | 'number'
    | 'datePicker'
    | 'rangePicker'
    | 'checkbox'
    | 'select';
  /** Значение */
  value: Record<string, unknown>;
  options?: { label: string; value: string }[];
  /** Плейсхолдер */
  placeholder?: string;
}

export type Table = ApiDefaultKeys & TableColumnsAndRows;

export interface WorkGroupTableProps {
  data: TableColumnsAndRows;
  isFullSize: boolean;
  isPending: boolean;
  refetch: Callback;
}

export interface SearchDrawerProps {
  handleClose: Callback;
  isOpened: boolean;
  refetchTable: Callback;
  refetchTableWithoutFilters?: Callback;
}
