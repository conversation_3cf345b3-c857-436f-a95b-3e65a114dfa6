import { Checkbox, DatePicker, Drawer, Input, Select, Typography } from 'antd';
import classNames from 'classnames';
import moment, { MomentInput } from 'moment';
import type { FC } from 'react';
import { useMemo } from 'react';
import {
  WorkGroupCreationPlanStore,
  SearchDrawerProps,
  SearchInputs,
} from 'widgets/WorkGroupCreationPlan';
import {
  DATE_VARIANTS_LIST,
  DEFAULT_DATE_VARIANT,
} from 'shared/config/constants';
import { useAppSelector } from 'shared/model';
import { useCreateSliceActions } from 'shared/model/useCreateSliceActions';
import { ButtonsContainer } from 'shared/ui/ButtonsContainer';

import styles from './styles.module.scss';

export const SearchDrawer: FC<SearchDrawerProps> = ({
  handleClose,
  isOpened,
  refetchTable,
  refetchTableWithoutFilters,
}) => {
  /* ----------------------------------------------------
   *                      Экшены
   ---------------------------------------------------- */
  const { handleResetSearch, handleSearchingInput } = useCreateSliceActions(
    WorkGroupCreationPlanStore.reducers.slice.actions,
  );

  /* ----------------------------------------------------
   *                      Селекторы
   ---------------------------------------------------- */
  const plan = useAppSelector(
    WorkGroupCreationPlanStore.selectors.planSelector,
  );

  /* ----------------------------------------------------
   *                      Колбеки
   ---------------------------------------------------- */

  const handleSubmitForm = (e: React.SyntheticEvent): void => {
    e.preventDefault();
    refetchTable();
  };

  /* ----------------------------------------------------
   *                      Мемоизация
   ---------------------------------------------------- */
  const memoSearch = useMemo<SearchInputs[]>(
    () => [
      {
        title: 'Регистрационный номер ПЛ',
        key: 'regNum',
        type: 'default',
        value: { regNum: plan.searchInputs.regNum || '' },
        placeholder: 'Регистрационный номер',
      },
      {
        title: 'Показывать филиалы',
        key: 'showFilial',
        type: 'checkbox',
        value: { showFilial: plan.searchInputs.showFilial || '' },
        placeholder: 'Выполнять поиск с учетом филиалов',
      },
      {
        title: 'ИНН ПЛ',
        key: 'inn',
        type: 'number',
        value: { inn: plan.searchInputs.inn || '' },
        placeholder: 'Инн организации',
      },
      {
        title: 'Наименование ПЛ',
        key: 'name',
        type: 'default',
        value: { name: plan.searchInputs.name || '' },
        placeholder: 'Наименование организации',
      },
      {
        title: 'Интервал планируемой даты начала проверки',
        key: 'plannedStart',
        type: 'rangePicker',
        value: {
          plannedStartFrom: plan.searchInputs.plannedStartFrom || '',
          plannedStartTo: plan.searchInputs.plannedStartTo || '',
        },
      },
      {
        title: 'Интервал даты создания КРГ',
        key: 'creationDate',
        type: 'rangePicker',
        value: {
          creationDateFrom: plan.searchInputs.creationDateFrom || '',
          creationDateTo: plan.searchInputs.creationDateTo || '',
        },
        placeholder: 'Планируемая дата создания КРГ',
      },

      {
        title: 'Месяц начала проверки',
        key: 'startMonth',
        type: 'select',
        value: { startMonth: plan.searchInputs.startMonth || '' },
        placeholder: 'Месяц начала проверки',
        options: [
          { value: '', label: '' },
          ...Array.from({ length: 12 }).map((_, i) => ({
            value: `${i + 1}`,
            label: `${i + 1}`,
          })),
        ],
      },
    ],
    [plan.searchInputs],
  );

  const searchButtons: AdditionalButton[] = [
    {
      loading: plan.table.isPending,
      title: 'Найти',
      key: 'search',
      type: 'primary',
      htmlType: 'submit',
      onClick: handleSubmitForm,
    },
    {
      loading: plan.table.isPending,
      title: 'Очистить',
      key: 'clean',
      danger: true,
      ghost: true,
      onClick: () => {
        handleResetSearch();
        // Используем функцию для получения данных без фильтров
        if (refetchTableWithoutFilters) {
          refetchTableWithoutFilters();
        } else {
          // Fallback: используем setTimeout чтобы дождаться обновления Redux состояния
          setTimeout(() => refetchTable(), 0);
        }
      },
    },
  ];

  /* ----------------------------------------------------
   *                      UI
   ---------------------------------------------------- */

  return (
    <Drawer
      title="Настроить фильтры"
      placement="right"
      closable
      onClose={handleClose}
      open={isOpened}
      getContainer={false}
      className={styles.drawer}
      width={700}
    >
      <form className={styles.search} onSubmit={handleSubmitForm}>
        <div className={styles.searchInputs}>
          {memoSearch.map((item) => (
            <div className={styles.searchInputsContainer} key={item.key}>
              <Typography.Text className={styles.searchInputsContainerText}>
                {item.title}
              </Typography.Text>
              {item.type === 'checkbox' ? (
                <Checkbox
                  checked={item.value[item.key] as boolean}
                  className={styles.searchInputsContainerInput}
                  onChange={(e) =>
                    handleSearchingInput({
                      [item.key]: !!e.target.checked,
                    })
                  }
                >
                  {item.placeholder}
                </Checkbox>
              ) : item.type === 'datePicker' ? (
                <DatePicker
                  className={styles.searchInputsContainerInput}
                  size="small"
                  placeholder={item.placeholder}
                  format={DATE_VARIANTS_LIST}
                  value={
                    item.value[item.key] === ''
                      ? null
                      : moment(
                          item.value[item.key] as MomentInput,
                          DEFAULT_DATE_VARIANT,
                        )
                  }
                  onChange={(_, dateString) =>
                    handleSearchingInput({
                      [item.key]: dateString,
                    })
                  }
                />
              ) : item.type === 'rangePicker' ? (
                <DatePicker.RangePicker
                  className={classNames(styles.rangePicker)}
                  format={DATE_VARIANTS_LIST}
                  placeholder={['Выберите дату', 'Выберите дату']}
                  value={
                    item.value[`${item.key}From`] !== '' &&
                    item.value[`${item.key}To`] !== ''
                      ? [
                          moment(
                            item.value[`${item.key}From`] as MomentInput,
                            DEFAULT_DATE_VARIANT,
                          ),
                          moment(
                            item.value[`${item.key}To`] as MomentInput,
                            DEFAULT_DATE_VARIANT,
                          ),
                        ]
                      : [null, null]
                  }
                  onChange={(_, dates) =>
                    handleSearchingInput({
                      [`${item.key}From`]: dates[0],
                      [`${item.key}To`]: dates[1],
                    })
                  }
                />
              ) : item.type === 'select' ? (
                <Select
                  value={item.value[item.key]}
                  className={styles.select}
                  size="small"
                  onChange={(v) => {
                    handleSearchingInput({
                      [item.key]: v,
                    });
                  }}
                  options={item.options}
                />
              ) : (
                <Input
                  maxLength={4000}
                  size="small"
                  type={item.type}
                  placeholder={item.placeholder}
                  onChange={(e) =>
                    handleSearchingInput({
                      [item.key]: e.target.value,
                    })
                  }
                  value={item.value[item.key] as string}
                  className={styles.searchInputsContainerInput}
                />
              )}
            </div>
          ))}
        </div>

        <ButtonsContainer buttons={searchButtons} />
      </form>
    </Drawer>
  );
};
