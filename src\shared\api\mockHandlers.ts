/* eslint-disable */
import { http, HttpResponse } from 'msw';
import { delay } from 'shared/lib/delay';

// Mock data imports
import {
  packageDefTableData,
  packageFilesData,
  packageFilesColumns,
  requestItemsData,
  requestItemsColumns,
} from './mockData';
import {
  eppTreeMockData,
  krgTreeRootMockData,
  reestry,
  resultaty,
} from './mockData/eppTreeMockData';
import { krg3RequestNoticeMockData } from './mockData/krg3RequestNoticeMockData';
import { krgTableMockData } from './mockData/krgTableMockData';
import { krg3NoticeForRequestMockData } from './mockData/notices';
import {
  assDocs,
  filenetMainFindDocuments,
  filenetPublicationGet,
  filenetRubrics,
  rubricTree,
} from './mockData/filenet-main-find-documents';
import { krg4CreationPlan } from './mockData/u-krg-table-mock-data';

// Constants
const DEFAULT_DELAY = 500;
const TREE_DELAY = 1000;

// ============================================================================
// KRG3 HANDLERS - Main table endpoints and nested data
// ============================================================================

/**
 * KRG3 main table handlers
 */
const krg3MainHandlers = [
  // Basic KRG3 table
  http.post('http://127.0.0.1:18080/kzid_rest/krg3', async ({ request }) => {
    const url = new URL(request.url);
    const page = url.searchParams.get('page') || '1';
    const size = url.searchParams.get('size') || '10';

    await delay(DEFAULT_DELAY);
    return HttpResponse.json(
      {
        ...krgTableMockData,
        page: parseInt(page, 10),
        size: parseInt(size, 10),
      },
      { status: 200 },
    );
  }),

  // Package definition table with lazy tabs
  http.post(
    'http://127.0.0.1:18080/kzid_rest/krg3_input_package_def',
    async ({ request }) => {
      const url = new URL(request.url);
      const pageNumber = url.searchParams.get('pageNumber') || '1';
      const pageSize = url.searchParams.get('pageSize') || '10';

      await delay(DEFAULT_DELAY);
      return HttpResponse.json(
        {
          ...packageDefTableData,
          pagination: {
            total: 3,
            pageSize: parseInt(pageSize, 10),
          },
          pageNumber: parseInt(pageNumber, 10),
        },
        { status: 200 },
      );
    },
  ),

  // Request notice table with lazy tabs
  http.post(
    'http://127.0.0.1:18080/kzid_rest/krg3_request_notice',
    async ({ request }) => {
      const url = new URL(request.url);
      const pageNumber = url.searchParams.get('pageNumber') || '1';
      const pageSize = url.searchParams.get('pageSize') || '10';

      await delay(DEFAULT_DELAY);
      return HttpResponse.json(
        krg3RequestNoticeMockData(pageNumber, pageSize),
        { status: 200 },
      );
    },
  ),
];

/**
 * KRG3 nested table handlers - individual tab content loading
 */
const krg3NestedHandlers = [
  // Package files tab content (lazy loading)
  http.get(
    'http://127.0.0.1:18080/kzid_rest/krg3_package_files_def',
    async ({ request }) => {
      const url = new URL(request.url);
      const pageNumber = parseInt(
        url.searchParams.get('pageNumber') || '1',
        10,
      );
      const pageSize = parseInt(url.searchParams.get('pageSize') || '10', 10);
      const packageId = url.searchParams.get('packageId');

      console.log('Loading package files tab:', { pageNumber, packageId });
      await delay(DEFAULT_DELAY);

      const startIndex = (pageNumber - 1) * pageSize;
      const endIndex = startIndex + pageSize;
      const pageFiles = packageFilesData.slice(startIndex, endIndex);

      return HttpResponse.json(
        {
          columns: packageFilesColumns,
          rows: pageFiles,
          pagination: {
            total: packageFilesData.length,
            pageSize,
          },
          pageNumber,
          sort: null,
          group: null,
        },
        { status: 200 },
      );
    },
  ),

  // Request items tab content (lazy loading)
  http.get(
    'http://127.0.0.1:18080/kzid_rest/krg3_request_item',
    async ({ request }) => {
      const url = new URL(request.url);
      const pageNumber = parseInt(
        url.searchParams.get('pageNumber') || '1',
        10,
      );
      const pageSize = parseInt(url.searchParams.get('pageSize') || '10', 10);

      await delay(600);

      const startIndex = (pageNumber - 1) * pageSize;
      const endIndex = startIndex + pageSize;
      const pageItems = requestItemsData.slice(startIndex, endIndex);

      return HttpResponse.json(
        {
          columns: requestItemsColumns,
          rows: pageItems,
          pagination: {
            total: requestItemsData.length,
            pageSize,
          },
          pageNumber,
          sort: null,
          group: null,
        },
        { status: 200 },
      );
    },
  ),

  // Notice for request tab content (lazy loading)
  http.post(
    'http://127.0.0.1:18080/kzid_rest/krg3_notice_for_request',
    async ({ request }) => {
      const url = new URL(request.url);
      const pageNumber = parseInt(
        url.searchParams.get('pageNumber') || '1',
        10,
      );
      const pageSize = parseInt(url.searchParams.get('pageSize') || '10', 10);

      await delay(700);

      const mockData = krg3NoticeForRequestMockData(
        Number(pageNumber),
        pageSize,
      );
      console.log('krg3_notice_for_request mock data:', {
        pageNumber,
        pageSize,
        totalRows: mockData.rows.length,
        totalInPagination: mockData.pagination.total,
        paginationPageSize: mockData.pagination.pageSize,
      });

      return HttpResponse.json(mockData, { status: 200 });
    },
  ),
];

/**
 * KRG3 legacy batch loading handlers - loads all tabs at once (old mode)
 */
const krg3LegacyHandlers = [
  // Legacy: Load all tabs at once when row is expanded (old lazy mode)
  http.post(
    'http://127.0.0.1:18080/kzid_rest/krg3_request_notice/tabs',
    async ({ request }) => {
      const url = new URL(request.url);
      const requestId = url.searchParams.get('requestNoticeId');

      await delay(800);
      console.log('Loading tabs for requestNoticeId:', requestId);

      // Return structure as in old mode - array of tabs directly
      return HttpResponse.json(
        {
          tabs: [
            {
              label: 'Пункты заявки',
              key: '1',
              endpoint: 'krg3_request_item',
              tableData: {
                columns: requestItemsColumns,
                rows: requestItemsData.slice(0, 5),
                pagination: {
                  total: requestItemsData.length,
                  pageSize: 10,
                },
                pageNumber: 1,
              },
            },
            {
              label: 'Уведомления, связанные с заявкой',
              key: '4',
              endpoint: 'krg3_notice_for_request',
              tableData: {
                columns: [
                  {
                    title: 'Номер уведомления',
                    dataIndex: 'noticeNumber',
                    key: 'noticeNumber',
                    columnType: 'String',
                    width: 150,
                  },
                  {
                    title: 'Дата создания',
                    dataIndex: 'dateCreate',
                    key: 'dateCreate',
                    columnType: 'String',
                    width: 150,
                  },
                  {
                    title: 'Статус',
                    dataIndex: 'status',
                    key: 'status',
                    columnType: 'String',
                    width: 150,
                  },
                ],
                rows: [
                  {
                    key: 'notice-1',
                    noticeNumber: 'УВД-001',
                    dateCreate: '15.11.2022 10:30:00',
                    status: 'Отправлено',
                    rowId: { id: 'notice-1' },
                  },
                  {
                    key: 'notice-2',
                    noticeNumber: 'УВД-002',
                    dateCreate: '16.11.2022 14:20:00',
                    status: 'В работе',
                    rowId: { id: 'notice-2' },
                  },
                ],
                pagination: {
                  total: 2,
                  pageSize: 10,
                },
                pageNumber: 1,
              },
            },
          ],
        },
        { status: 200 },
      );
    },
  ),
];

// ============================================================================
// EPP TREE HANDLERS - Tree structure endpoints
// ============================================================================

/**
 * EPP Tree handlers - Tree structure and file visibility endpoints
 */
const eppTreeHandlers = [
  http.get(
    'http://127.0.0.1:18080/kzid_rest/krg/epc-tree',
    async ({ request }) => {
      const url = new URL(request.url);
      const itemId = url.searchParams.get('itemId');
      const pos = url.searchParams.get('pos');
      const cabinetId = url.searchParams.get('cabinetId');
      const user = url.searchParams.get('user');
      const isCabinet = url.searchParams.get('isCabinet') === 'true';

      await delay(TREE_DELAY);

      // Root cabinet request
      if (
        cabinetId === 'bd44a562-fcae-45b6-b38f-539e7b69ade9' &&
        user === 'admin' &&
        isCabinet === true
      ) {
        return HttpResponse.json(
          {
            ...krgTreeRootMockData,
            requestParams: { itemId, pos, cabinetId, user, isCabinet },
          },
          { status: 200 },
        );
      }

      // Specific tree nodes
      const treeNodeMap: Record<string, any> = {
        '4109a8e1-7b82-44b5-b33a-ee978c425517': eppTreeMockData,
        '389c9fe9-b118-4773-8e2a-327a183a4763': reestry,
        '1af7bc7c-57e7-4d69-992b-16f635a0fe02': resultaty,
        '': reestry, // Empty itemId case
      };

      const mockData = treeNodeMap[itemId || ''];
      if (mockData) {
        return HttpResponse.json(
          {
            ...mockData,
            requestParams: { itemId, pos, cabinetId, user, isCabinet },
          },
          { status: 200 },
        );
      }

      // Pass through to real API for unhandled requests
      return undefined;
    },
  ),

  // File visibility toggle
  http.put(
    'http://127.0.0.1:18080/kzid_rest/krg3_file_data/visibility',
    async () => {
      await delay(0);
      return new HttpResponse(null, { status: 204 });
    },
  ),
];

// ============================================================================
// FILENET HANDLERS - Document management endpoints
// ============================================================================

/**
 * Filenet handlers - Document management and search endpoints
 */
const filenetHandlers = [
  // Document search
  http.post('http://127.0.0.1:18080/filenet/main/find/documents', async () => {
    await delay(DEFAULT_DELAY);
    return HttpResponse.json(filenetMainFindDocuments, { status: 200 });
  }),

  // Publication details
  http.get('http://127.0.0.1:18080/filenet/publication/get/1226', async () => {
    await delay(DEFAULT_DELAY);
    return HttpResponse.json(filenetPublicationGet, { status: 200 });
  }),

  // Rubrics associations
  http.post(
    'http://127.0.0.1:18080/filenet/publication/audit/associations/rubrics/get',
    async () => {
      await delay(DEFAULT_DELAY);
      return HttpResponse.json(filenetRubrics, { status: 200 });
    },
  ),

  // Associated documents
  http.post(
    'http://127.0.0.1:18080/filenet/publication/audit/associations/assdocs/get',
    async () => {
      await delay(DEFAULT_DELAY);
      return HttpResponse.json(assDocs, { status: 200 });
    },
  ),

  // Rubric tree
  http.get(
    'http://127.0.0.1:18080/filenet/rubric/find/as/tree',
    async () => {
      await delay(DEFAULT_DELAY);
      return HttpResponse.json(rubricTree, { status: 200 });
    },
  ),
];

// ============================================================================
// UTILITY HANDLERS - System and auxiliary endpoints
// ============================================================================

/**
 * Utility handlers - System status, progress, and other auxiliary endpoints
 */
const utilityHandlers = [
  // Replicator progress status
  http.get(
    'http://127.0.0.1:18080/kzid_rest/replicator_progress',
    async () => {
      await delay(DEFAULT_DELAY);
      return HttpResponse.json(
        {
          isLoading: false,
          current: 0,
          total: 0,
          currentFinishDate: '25.12.2024 18:28:09',
          text: null,
        },
        { status: 200 },
      );
    },
  ),

  // KRG4 creation plan
  http.post(
    'http://127.0.0.1:18080/kzid_rest/krg4_creation_plan',
    async () => {
      await delay(DEFAULT_DELAY);
      return HttpResponse.json(krg4CreationPlan, { status: 200 });
    },
  ),
];

// ============================================================================
// EXPORT ALL HANDLERS
// ============================================================================

/**
 * Combined mock handlers for MSW
 * Organized by functionality for better maintainability
 */
export const handlers = [
  // KRG3 endpoints
  ...krg3MainHandlers,
  ...krg3NestedHandlers,
  ...krg3LegacyHandlers,

  // Tree structure endpoints
  ...eppTreeHandlers,

  // Document management endpoints
  ...filenetHandlers,

  // System and utility endpoints
  ...utilityHandlers,
];
